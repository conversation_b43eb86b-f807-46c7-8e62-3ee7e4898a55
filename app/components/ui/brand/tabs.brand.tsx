import * as React from 'react';

import {
    Tabs as BaseTabs,
    <PERSON><PERSON><PERSON>ontent as BaseTabsContent,
    <PERSON><PERSON><PERSON>ist as BaseTabsList,
    TabsTrigger as BaseTabsTrigger,
} from '~/components/ui/base';
import { cn } from '~/lib/utils';

// 品牌封装：仅在基础组件上使用主题色作为字体颜色
// 用法：从此文件导入 TabsBrand* 组件替代基础 Tabs*

const TabsBrand = BaseTabs;

const TabsBrandList = React.forwardRef<
    React.ComponentRef<typeof BaseTabsList>,
    React.ComponentPropsWithoutRef<typeof BaseTabsList>
>(({ className, ...props }, ref) => (
    <BaseTabsList
        ref={ref}
        className={cn(
            // 移除默认的背景和圆角，改为下划线样式
            'border-border h-auto w-full border-b bg-transparent p-0',
            className,
        )}
        {...props}
    />
));
TabsBrandList.displayName = 'TabsBrandList';

const TabsBrandTrigger = React.forwardRef<
    React.ComponentRef<typeof BaseTabsTrigger>,
    React.ComponentPropsWithoutRef<typeof BaseTabsTrigger>
>(({ className, ...props }, ref) => (
    <BaseTabsTrigger
        ref={ref}
        className={cn(
            // 移除默认样式，使用下划线样式
            'relative h-auto rounded-none border-0 bg-transparent px-4 py-3 font-normal shadow-none',
            // 文字颜色
            'text-muted-foreground hover:text-foreground',
            'data-[state=active]:text-lilith-primary',
            'dark:data-[state=active]:text-lilith-primary',
            // 下划线效果 - 使用 after 伪元素
            'after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:bg-transparent after:transition-colors',
            'data-[state=active]:after:bg-lilith-primary',
            // 移除焦点样式冲突
            'focus-visible:ring-0 focus-visible:outline-none',
            className,
        )}
        {...props}
    />
));
TabsBrandTrigger.displayName = 'TabsBrandTrigger';

const TabsBrandContent = React.forwardRef<
    React.ComponentRef<typeof BaseTabsContent>,
    React.ComponentPropsWithoutRef<typeof BaseTabsContent>
>(({ className, ...props }, ref) => (
    <BaseTabsContent ref={ref} className={className} {...props} />
));
TabsBrandContent.displayName = 'TabsBrandContent';

export { TabsBrand, TabsBrandContent, TabsBrandList, TabsBrandTrigger };
