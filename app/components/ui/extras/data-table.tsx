import * as React from 'react';

import {
    type ColumnDef,
    type ColumnFiltersState,
    type SortingState,
    type VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from '@tanstack/react-table';

import {
    Button,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '~/components/ui/base';
import { cn } from '~/lib/utils';

interface DataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
    data: TData[];
    // 简单模式配置
    simple?: boolean;
    // 高级模式配置
    enableSorting?: boolean;
    enableFiltering?: boolean;
    enablePagination?: boolean;
    enableRowSelection?: boolean;
    // 样式配置
    className?: string;
    headerClassName?: string;
    cellClassName?: string;
    // 加载状态
    isLoading?: boolean;
    loadingComponent?: React.ReactNode;
    // 空状态
    emptyComponent?: React.ReactNode;
    emptyText?: string;
    // 分页配置
    pageSize?: number;
    // 事件回调
    onRowClick?: (row: TData) => void;
}

export function DataTable<TData, TValue>({
    columns,
    data,
    simple = false,
    enableSorting = true,
    enableFiltering = true,
    enablePagination = false,
    enableRowSelection = false,
    className,
    headerClassName,
    cellClassName,
    isLoading = false,
    loadingComponent,
    emptyComponent,
    emptyText = '暂无数据',
    pageSize = 10,
    onRowClick,
}: DataTableProps<TData, TValue>) {
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] =
        React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] =
        React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState({});

    const table = useReactTable({
        data,
        columns,
        onSortingChange: enableSorting && !simple ? setSorting : undefined,
        onColumnFiltersChange:
            enableFiltering && !simple ? setColumnFilters : undefined,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel:
            enablePagination && !simple ? getPaginationRowModel() : undefined,
        getSortedRowModel:
            enableSorting && !simple ? getSortedRowModel() : undefined,
        getFilteredRowModel:
            enableFiltering && !simple ? getFilteredRowModel() : undefined,
        onColumnVisibilityChange: !simple ? setColumnVisibility : undefined,
        onRowSelectionChange:
            enableRowSelection && !simple ? setRowSelection : undefined,
        initialState: {
            pagination: {
                pageSize,
            },
        },
        state: simple
            ? {}
            : {
                  sorting,
                  columnFilters,
                  columnVisibility,
                  rowSelection,
              },
    });

    const renderEmptyState = () => {
        if (emptyComponent) {
            return emptyComponent;
        }
        return (
            <TableRow>
                <TableCell
                    colSpan={columns.length}
                    className='h-24 text-center'
                >
                    <div className='text-muted-foreground text-sm'>
                        {emptyText}
                    </div>
                </TableCell>
            </TableRow>
        );
    };

    const renderLoadingState = () => {
        if (loadingComponent) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className='h-24 p-0'>
                        {loadingComponent}
                    </TableCell>
                </TableRow>
            );
        }
        return (
            <TableRow>
                <TableCell
                    colSpan={columns.length}
                    className='h-24 text-center'
                >
                    <div className='text-muted-foreground text-sm'>
                        加载中...
                    </div>
                </TableCell>
            </TableRow>
        );
    };

    return (
        <div className='space-y-4'>
            {/* 表格 */}
            <div className={cn('overflow-hidden rounded-md border', className)}>
                <Table>
                    <TableHeader className={cn('bg-muted', headerClassName)}>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header, index) => (
                                    <TableHead
                                        key={header.id}
                                        className={cn(
                                            'text-muted-foreground text-xs',
                                            index === 0 &&
                                                (enableRowSelection
                                                    ? 'pl-4'
                                                    : 'pl-8'),
                                            headerClassName,
                                        )}
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                  header.column.columnDef
                                                      .header,
                                                  header.getContext(),
                                              )}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {isLoading
                            ? renderLoadingState()
                            : table.getRowModel().rows?.length
                              ? table.getRowModel().rows.map((row) => (
                                    <TableRow
                                        key={row.id}
                                        data-state={
                                            row.getIsSelected() && 'selected'
                                        }
                                        className={
                                            onRowClick ? 'cursor-pointer' : ''
                                        }
                                        onClick={() =>
                                            onRowClick?.(row.original)
                                        }
                                    >
                                        {row
                                            .getVisibleCells()
                                            .map((cell, index) => (
                                                <TableCell
                                                    key={cell.id}
                                                    className={cn(
                                                        'whitespace-nowrap',
                                                        index === 0 && 'pl-4',
                                                        cellClassName,
                                                    )}
                                                >
                                                    {flexRender(
                                                        cell.column.columnDef
                                                            .cell,
                                                        cell.getContext(),
                                                    )}
                                                </TableCell>
                                            ))}
                                    </TableRow>
                                ))
                              : renderEmptyState()}
                    </TableBody>
                </Table>
            </div>

            {/* 分页控件 */}
            {enablePagination && !simple && (
                <div className='flex items-center justify-end space-x-2'>
                    <div className='text-muted-foreground flex-1 text-sm'>
                        {enableRowSelection && (
                            <>
                                已选择{' '}
                                {
                                    table.getFilteredSelectedRowModel().rows
                                        .length
                                }{' '}
                                行，
                            </>
                        )}
                        共 {table.getFilteredRowModel().rows.length} 行
                    </div>
                    <div className='space-x-2'>
                        <Button
                            variant='outline'
                            size='sm'
                            onClick={() => table.previousPage()}
                            disabled={!table.getCanPreviousPage()}
                        >
                            上一页
                        </Button>
                        <Button
                            variant='outline'
                            size='sm'
                            onClick={() => table.nextPage()}
                            disabled={!table.getCanNextPage()}
                        >
                            下一页
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
}

// 导出类型
export type { DataTableProps };
