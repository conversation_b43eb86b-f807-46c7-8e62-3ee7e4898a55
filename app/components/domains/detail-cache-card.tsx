import { Link } from 'react-router';

import { type ColumnDef } from '@tanstack/react-table';
import { PlusIcon } from 'lucide-react';

import { ActionButton, DataTable } from '~/components/ui';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '~/components/ui/base';

interface CacheRuleProp {
    id: string;
    status: string;
    priority: number;
    matchType: string;
    matchMethod: string;
    matchContent: string;
    cacheTime: string;
}

interface CacheExpirationProp {
    browserCacheTime: string;
    edgeCacheTime: string;
    maxCacheTime: string;
    ignoreCacheControl: string;
    ignoreSetCookie: string;
    ignoreAuthHeader: string;
}

interface CacheConfigProp {
    cacheRules: CacheRuleProp[];
    cacheExpiration: CacheExpirationProp;
}

// 缓存规则表格列定义
const getCacheRulesColumns = (): ColumnDef<CacheRuleProp>[] => [
    {
        accessorKey: 'priority',
        header: '优先级',
        cell: ({ row }) => (
            <div className='flex items-center pl-4'>
                <Badge
                    variant='destructive'
                    className='bg-lilith-primary/80 text-lilith-primary-foreground h-6 w-6 rounded-sm'
                >
                    {row.getValue('priority')}
                </Badge>
            </div>
        ),
    },
    {
        accessorKey: 'matchType',
        header: '匹配规则',
        cell: ({ row }) => {
            const rule = row.original;
            return (
                <div className='space-y-2'>
                    <div className='grid grid-cols-[auto_1fr] gap-x-4 gap-y-1 text-xs'>
                        <span className='text-muted-foreground'>匹配对象</span>
                        <span>{rule.matchType}</span>
                        <span className='text-muted-foreground'>匹配方式</span>
                        <span>{rule.matchMethod}</span>
                        <span className='text-muted-foreground'>匹配内容</span>
                        <span className='font-mono'>{rule.matchContent}</span>
                    </div>
                </div>
            );
        },
    },
    {
        accessorKey: 'cacheTime',
        header: '缓存时间',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('cacheTime')}</span>
        ),
    },
    // {
    //     id: 'actions',
    //     header: '操作',
    //     cell: () => (
    //         <div className='flex items-center gap-2'>
    //             <Button variant='ghost' size='sm' className='h-6 px-2 text-xs'>
    //                 编辑
    //             </Button>
    //             <Button
    //                 variant='ghost'
    //                 size='sm'
    //                 className='h-6 px-2 text-xs text-red-600 hover:text-red-700'
    //             >
    //                 <TrashIcon className='h-3 w-3' />
    //             </Button>
    //         </div>
    //     ),
    // },
];

function DomainDetailCacheCard({ data }: { data: CacheConfigProp }) {
    return (
        <div className='space-y-6'>
            {/* 配置项提示 */}
            <div className='flex items-start gap-2 border-none p-2'>
                <p className='text-muted-foreground text-sm'>
                    配置项的值来自 CDN
                    服务商（简称"CDN"），不同"CDN"支持的具体配置项有差异，对于在"CDN"处不支持的配置项，展示"不支持"；对于在"CDN"处支持但值为空的情况，展示"-"。
                    详情请参考
                    <Button
                        asChild
                        variant='link'
                        size='sm'
                        className='px-0 text-sm'
                    >
                        <Link to='/docs/cdn-config'>文档</Link>
                    </Button>
                </p>
            </div>

            {/* 缓存规则 */}
            <Card className='border-none shadow-none'>
                <CardHeader className='flex flex-row items-center justify-between'>
                    <CardTitle className='font-medium'>缓存规则</CardTitle>
                    <ActionButton variant='secondary' size='xs'>
                        <PlusIcon className='mr-1' />
                        新增规则
                    </ActionButton>
                </CardHeader>
                <CardContent>
                    <p className='text-muted-foreground mb-4 text-sm'>
                        配置 CDN
                        对指定类型文件的缓存时间，让用户能够更快地获取资源。
                        详情请参考
                        <Button
                            asChild
                            variant='link'
                            size='sm'
                            className='px-0 text-xs'
                        >
                            <Link to='/docs/cache-rules'>文档</Link>
                        </Button>
                    </p>
                    <DataTable
                        columns={getCacheRulesColumns()}
                        data={data.cacheRules}
                        simple={true}
                        emptyText='暂无缓存规则'
                    />
                </CardContent>
            </Card>

            {/* 缓存过期时间 */}
            <Card className='w-[80%] border-none shadow-none'>
                <CardHeader>
                    <CardTitle className='font-medium'>缓存过期时间</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className='text-muted-foreground mb-6 text-sm'>
                        配置 CDN
                        对指定类型文件的缓存过期时间，让用户能够更快地获取资源。
                    </p>
                    <div className='grid grid-cols-1 gap-8 md:grid-cols-2'>
                        <div className='grid grid-cols-[auto_1fr] gap-x-8 gap-y-6'>
                            <span className='text-muted-foreground text-sm'>
                                浏览器缓存时间
                            </span>
                            <span className='text-sm'>
                                {data.cacheExpiration.browserCacheTime}
                            </span>

                            <span className='text-muted-foreground text-sm'>
                                边缘缓存时间
                            </span>
                            <span className='text-sm'>
                                {data.cacheExpiration.edgeCacheTime}
                            </span>

                            <span className='text-muted-foreground text-sm'>
                                最大缓存时间
                            </span>
                            <span className='text-sm'>
                                {data.cacheExpiration.maxCacheTime}
                            </span>
                        </div>

                        <div className='grid grid-cols-[auto_1fr] gap-x-8 gap-y-6'>
                            <span className='text-muted-foreground text-sm'>
                                忽略缓存控制
                            </span>
                            <span className='text-sm'>
                                {data.cacheExpiration.ignoreCacheControl}
                            </span>

                            <span className='text-muted-foreground text-sm'>
                                忽略Set-Cookie
                            </span>
                            <span className='text-sm'>
                                {data.cacheExpiration.ignoreSetCookie}
                            </span>

                            <span className='text-muted-foreground text-sm'>
                                忽略认证头
                            </span>
                            <span className='text-sm'>
                                {data.cacheExpiration.ignoreAuthHeader}
                            </span>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}

export { DomainDetailCacheCard, type CacheConfigProp };
