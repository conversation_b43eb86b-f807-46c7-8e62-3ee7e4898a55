import { Link } from 'react-router';

import type { ColumnDef } from '@tanstack/react-table';
import { ExternalLink } from 'lucide-react';

import { CloudIconWithText, CopyButton, DataTable } from '~/components/ui';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '~/components/ui/base';
import type { CloudProvider } from '~/types/cloud';

export interface OriginConfigProp {
    type: string;
    address: string;
    priority: number;
    weight: number;
    backupAddress: string;
    backupHost: string;
    protocol: string;
    httpPort: number;
    httpsPort: number;
    tcpKeepAlive: string;
    httpRequestHeader: string;
}

// 此处定义下 domainData 的类型
export interface DomainDataProp {
    domain: string;
    provider: CloudProvider;
    cname: string;
    createTime: string;
    tags: Record<string, string>;
    accelerationType: string;
    accelerationRegion: string;
    lastModified: string;
    project: string;
    originConfig: OriginConfigProp;
    backToOriginProtocol: {
        allowHttp: string;
        allowHttps: string;
    };
}

function DomainDetailBasicCardBody({ data }: { data: DomainDataProp }) {
    return (
        <div className='grid grid-cols-1 gap-8 md:grid-cols-2'>
            <div className='grid grid-cols-[auto_1fr] gap-x-8 gap-y-3'>
                <span className='text-muted-foreground text-sm'>域名</span>
                <div className='flex items-center gap-2'>
                    <span className='font-mono text-xs'>{data.domain}</span>
                    <CopyButton
                        variant='ghost'
                        size='sm'
                        text={data.domain}
                        showIcon={true}
                    />
                </div>

                <span className='text-muted-foreground text-sm'>云服务商</span>
                <div>
                    <CloudIconWithText size='14' provider={data.provider} />
                </div>

                <span className='text-muted-foreground text-sm'>CNAME</span>
                <div className='flex items-center gap-2'>
                    <span className='font-mono text-xs'>{data.cname}</span>
                    <CopyButton
                        text={data.cname}
                        variant='ghost'
                        size='sm'
                        showIcon={true}
                    />
                </div>

                <span className='text-muted-foreground text-sm'>创建时间</span>
                <span className='text-xs'>{data.createTime}</span>

                <span className='text-muted-foreground text-sm'>标签</span>
                <div className='flex flex-wrap gap-1'>
                    {Object.entries(data.tags).map(([key, value]) => (
                        <Badge key={key} variant='outline' className='text-xs'>
                            {key}:{value}
                        </Badge>
                    ))}
                </div>
            </div>

            <div className='grid grid-cols-[auto_1fr] gap-x-8 gap-y-3'>
                <span className='text-muted-foreground text-sm'>加速类型</span>
                <span className='text-xs'>{data.accelerationType}</span>

                <span className='text-muted-foreground text-sm'>加速区域</span>
                <span className='text-xs'>{data.accelerationRegion}</span>

                <span className='text-muted-foreground text-sm'>
                    最近修改时间
                </span>
                <span className='text-xs'>{data.lastModified}</span>

                <span className='text-muted-foreground text-sm'>项目组</span>
                <div>
                    <Badge asChild variant='secondary'>
                        <Link to='#'>
                            {data.project}
                            <ExternalLink className='ml-1 h-2 w-2' />
                        </Link>
                    </Badge>
                </div>
            </div>
        </div>
    );
}

const getColums = (): ColumnDef<OriginConfigProp>[] => [
    {
        accessorKey: 'type',
        header: '类型',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('type')}</span>
        ),
    },
    {
        accessorKey: 'address',
        header: '回源地址',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('address')}</span>
        ),
    },
    {
        accessorKey: 'priority',
        header: '优先级',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('priority')}</span>
        ),
    },
    {
        accessorKey: 'weight',
        header: '权重',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('weight')}</span>
        ),
    },
    {
        accessorKey: 'backupAddress',
        header: '回源Host类型',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('backupAddress')}</span>
        ),
    },
    {
        accessorKey: 'backupHost',
        header: '回源Host',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('backupHost')}</span>
        ),
    },

    {
        accessorKey: 'protocol',
        header: '回源协议',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('protocol')}</span>
        ),
    },
    {
        accessorKey: 'httpPort',
        header: 'HTTP端口',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('httpPort')}</span>
        ),
    },
    {
        accessorKey: 'httpsPort',
        header: 'HTTPs端口',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('httpsPort')}</span>
        ),
    },
    {
        accessorKey: 'tcpKeepAlive',
        header: 'TCP长连接',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('tcpKeepAlive')}</span>
        ),
    },
    {
        accessorKey: 'httpRequestHeader',
        header: 'HTTP请求超时',
        cell: ({ row }) => (
            <span className='text-xs'>{row.getValue('httpRequestHeader')}</span>
        ),
    },
];

export function DomainDetailBasicCard({ data }: { data: DomainDataProp }) {
    return (
        <div className='space-y-6'>
            {/* 配置项提示 */}
            <div className='flex items-start gap-2 rounded-sm border-none p-2'>
                {/* <p className='text-lilith-muted-foreground/50 dark:text-muted-foreground text-xs'> */}
                <p className='text-muted-foreground text-sm'>
                    配置项的值来自 CDN
                    服务商（简称"CDN"），不同"CDN"支持的具体配置项有差异，对于在"CDN"处不支持的配置项，展示"不支持"；对于在"CDN"处支持但值信息为空的情况，展示"-"。
                    详情请参考
                    <Button
                        asChild
                        variant='link'
                        size='sm'
                        className='px-0 text-sm'
                    >
                        <Link to='/docs/cdn-config'>文档</Link>
                    </Button>
                </p>
            </div>

            {/* 基本信息 */}
            <Card className='max-w-[90%] border-none shadow-none'>
                <CardHeader>
                    <CardTitle>基本信息</CardTitle>
                </CardHeader>
                <CardContent>
                    <DomainDetailBasicCardBody data={data} />
                </CardContent>
            </Card>

            {/* 回源站配置 */}
            <Card className='border-none shadow-none'>
                <CardHeader>
                    <CardTitle>回源站配置</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className='text-muted-foreground mb-4 text-sm'>
                        CDN
                        回源时对源站的域名设置及验证级别的源站配置，源站级别配置会对源站下下方的各源站地址生效。
                    </p>
                    <DataTable
                        columns={getColums()}
                        data={[data.originConfig]}
                        simple={true}
                    />
                </CardContent>
            </Card>

            {/* 回源协议与端口 */}
            <Card className='w-[60%] border-none shadow-none'>
                <CardHeader>
                    <CardTitle>回源协议与端口</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className='text-muted-foreground mb-6 text-sm'>
                        CDN
                        回源时使用的协议及端口，全局回源协议会对源站下方的各源站地址生效。
                    </p>
                    <div className='grid grid-cols-1 gap-8 md:grid-cols-2'>
                        <div className='grid grid-cols-[auto_1fr] gap-x-8'>
                            <span className='text-muted-foreground text-sm'>
                                全局回源协议
                            </span>
                            <span className='text-sm'>
                                {data.backToOriginProtocol.allowHttp}
                            </span>
                        </div>
                        <div className='grid grid-cols-[auto_1fr] gap-x-8'>
                            <span className='text-muted-foreground text-sm'>
                                全局Https回源
                            </span>
                            <span className='text-sm'>
                                {data.backToOriginProtocol.allowHttps}
                            </span>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
