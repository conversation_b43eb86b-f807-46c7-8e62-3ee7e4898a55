import { useState } from 'react';

import { Link } from 'react-router';

import {
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getSortedRowModel,
    useReactTable,
    type ColumnDef,
    type ColumnFiltersState,
    type SortingState,
} from '@tanstack/react-table';
import { Edit, Info, MoreHorizontal, Trash2 } from 'lucide-react';

import { DomainsTableLoading } from '~/components/domains/table-loading';
import {
    CloudIconWithText,
    CopyButton,
    DomainStatusBadge,
} from '~/components/ui';
import {
    Badge,
    Button,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '~/components/ui/base';
import { cn } from '~/lib/utils';
import type {
    Domain,
    DomainStatus,
    HttpsStatus,
    TrafficSchedulingStatus,
} from '~/types/domain';
import {
    getAccelerationRegionText,
    getAccelerationTypeText,
    getDomainAccelerationStatusText,
    getHttpsStatusText,
    getTrafficSchedulingText,
} from '~/types/domain';

interface DomainsTableProps {
    domains: Domain[];
    isLoading?: boolean;
    onToggleTrafficScheduling?: (domainId: string) => void;
    // onToggleHttps?: (domainId: string) => void;
    onEdit?: (domain: Domain) => void;
    onDelete?: (domainId: string) => void;
    // onRefresh?: (domainId: string) => void;
}

export function DomainsTable({
    domains,
    isLoading = false,
    onToggleTrafficScheduling: _onToggleTrafficScheduling,
    onEdit,
    onDelete,
}: DomainsTableProps) {
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

    const columns: ColumnDef<Domain>[] = [
        {
            accessorKey: 'domain',
            header: '域名',
            maxSize: 10, // 设置最大宽度
            cell: ({ row }) => {
                const domain = row.original.domain;

                // 简单估算：假设每个字符约8px宽度，250px大约能容纳31个字符
                const isLongDomain = domain.length > 31;

                const domainButton = (
                    <Button
                        asChild
                        variant='link'
                        className='hover:text-lilith-primary max-w-[250px] truncate pr-1 text-xs font-normal tracking-normal'
                    >
                        <Link to={`/domains/${domain}`} className='truncate'>
                            {domain}
                        </Link>
                    </Button>
                );

                return (
                    <div className='group flex max-w-[300px] items-center'>
                        {isLongDomain ? (
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    {domainButton}
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>{domain}</p>
                                </TooltipContent>
                            </Tooltip>
                        ) : (
                            domainButton
                        )}

                        <CopyButton
                            text={domain}
                            className='h-6 w-6 flex-shrink-0 p-0 opacity-0 transition-opacity group-hover:opacity-100'
                            iconSize='h-2 w-2'
                        />
                    </div>
                );
            },
        },
        {
            accessorKey: 'trafficScheduling',
            header: () => (
                <div className='flex items-center gap-1'>
                    多云流量调度
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Info className='text-muted-foreground size-3 cursor-pointer' />
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>
                                启用后将根据调度策略在多个云服务商之间分配流量
                            </p>
                        </TooltipContent>
                    </Tooltip>
                </div>
            ),
            cell: ({ row }) => {
                const status = row.getValue(
                    'trafficScheduling',
                ) as TrafficSchedulingStatus;
                const isEnabled = status === 'enabled';
                return (
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                variant='link'
                                className={cn(
                                    'cursor-pointer underline decoration-dotted underline-offset-4',
                                    isEnabled
                                        ? 'text-green-700 dark:text-green-600'
                                        : 'text-muted-foreground',
                                )}
                                asChild
                            >
                                <span className='text-xs font-normal'>
                                    {getTrafficSchedulingText(status)}
                                </span>
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>{getTrafficSchedulingText(status)}</p>
                        </TooltipContent>
                    </Tooltip>
                );
            },
        },
        {
            accessorKey: 'provider',
            header: '云服务商',
            cell: ({ row }) => {
                return (
                    <div className='flex items-center gap-2'>
                        <CloudIconWithText
                            provider={row.getValue('provider')}
                            size={16}
                        />
                    </div>
                );
            },
        },
        {
            accessorKey: 'product',
            header: '产品',
            cell: ({ row }) => {
                return (
                    <span className='text-xs'>{row.getValue('product')}</span>
                );
            },
        },
        {
            accessorKey: 'status',
            header: '加速状态',
            cell: ({ row }) => {
                const status = row.original.status;

                // 创建一个适配器函数来处理类型转换
                const statusTextAdapter = (statusStr: string) => {
                    return getDomainAccelerationStatusText(
                        statusStr as DomainStatus,
                    );
                };

                return (
                    <DomainStatusBadge
                        status={status}
                        getStatusText={statusTextAdapter}
                    />
                );
            },
        },
        {
            accessorKey: 'accelerationType',
            header: '加速类型',
            cell: ({ row }) => {
                return (
                    <span className='text-xs'>
                        {getAccelerationTypeText(
                            row.getValue('accelerationType'),
                        )}
                    </span>
                );
            },
        },
        {
            accessorKey: 'accelerationRegion',
            header: '加速区域',
            cell: ({ row }) => {
                return (
                    <span className='text-xs'>
                        {getAccelerationRegionText(
                            row.getValue('accelerationRegion'),
                        )}
                    </span>
                );
            },
        },
        {
            accessorKey: 'httpsStatus',
            header: 'HTTPS',
            cell: ({ row }) => {
                const status = row.getValue('httpsStatus') as HttpsStatus;
                const isEnabled = status === 'enabled';
                return (
                    <div className='flex items-center gap-2'>
                        {isEnabled ? (
                            <span className='text-xs'>
                                {getHttpsStatusText(status)}
                            </span>
                        ) : (
                            <span className='text-lilith-primary text-xs'>
                                {getHttpsStatusText(status)}
                            </span>
                        )}
                    </div>
                );
            },
        },
        {
            accessorKey: 'tags',
            header: '标签',
            cell: ({ row }) => {
                const tags = row.getValue('tags') as string[];
                if (tags.length === 0) {
                    return (
                        <span className='text-muted-foreground text-xs'>-</span>
                    );
                }
                return (
                    <div className='flex flex-wrap gap-1'>
                        {tags.slice(0, 2).map((tag) => (
                            <Badge
                                key={tag}
                                variant='outline'
                                className='text-xs'
                            >
                                {tag}
                            </Badge>
                        ))}
                        {tags.length > 2 && (
                            <Badge variant='outline' className='text-xs'>
                                +{tags.length - 2}
                            </Badge>
                        )}
                    </div>
                );
            },
        },
        {
            accessorKey: 'projectGroup',
            header: '项目组',
            cell: ({ row }) => {
                const projectGroup = row.getValue('projectGroup') as string;
                return <span className='text-xs'>{projectGroup || '-'}</span>;
            },
        },
        {
            accessorKey: 'lastUpdateTime',
            header: '数据更新时间',
            cell: ({ row }) => {
                return (
                    <span className='text-muted-foreground text-xs'>
                        {row.getValue('lastUpdateTime')}
                    </span>
                );
            },
        },
        {
            id: 'actions',
            header: '操作',
            cell: ({ row }) => {
                const domain = row.original;
                return (
                    <div className='flex items-center gap-2'>
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant='ghost' className='h-8 w-8 p-0'>
                                    <MoreHorizontal className='h-4 w-4' />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                                <DropdownMenuItem
                                    onClick={() => onEdit?.(domain)}
                                    className='flex items-center gap-2 text-xs'
                                >
                                    <Edit className='h-3 w-3' />
                                    编辑
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    onClick={() => onDelete?.(domain.id)}
                                    variant='destructive'
                                    className='flex items-center gap-2 text-xs'
                                >
                                    <Trash2 className='h-3 w-3' />
                                    删除
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                );
            },
        },
    ];

    const table = useReactTable({
        data: domains,
        columns,
        getCoreRowModel: getCoreRowModel(),
        onSortingChange: setSorting,
        getSortedRowModel: getSortedRowModel(),
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: getFilteredRowModel(),
        state: {
            sorting,
            columnFilters,
        },
    });

    return (
        <TooltipProvider>
            <div className='overflow-hidden rounded-md border'>
                <Table>
                    <TableHeader className='bg-muted'>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header, index) => (
                                    <TableHead
                                        key={header.id}
                                        className={cn(
                                            'text-muted-foreground text-xs',
                                            index === 0 && 'pl-8',
                                        )}
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                  header.column.columnDef
                                                      .header,
                                                  header.getContext(),
                                              )}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className='h-24 p-0'
                                >
                                    <DomainsTableLoading />
                                </TableCell>
                            </TableRow>
                        ) : table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={
                                        row.getIsSelected() && 'selected'
                                    }
                                >
                                    {row
                                        .getVisibleCells()
                                        .map((cell, index) => (
                                            <TableCell
                                                key={cell.id}
                                                className={cn(
                                                    'whitespace-nowrap',
                                                    index === 0 && 'pl-4',
                                                )}
                                            >
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext(),
                                                )}
                                            </TableCell>
                                        ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className='h-24 text-center'
                                >
                                    暂无数据
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
        </TooltipProvider>
    );
}

export type { Domain };
