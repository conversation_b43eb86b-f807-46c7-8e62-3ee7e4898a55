import * as React from 'react';

import { type ColumnDef } from '@tanstack/react-table';
import {
    ArrowUpDown,
    CheckIcon,
    HelpCircle,
    Loader2,
    MoreHorizontal,
    Pencil,
    RotateCcw,
    Trash2,
} from 'lucide-react';

import {
    ActionButton,
    CloudIconWithText,
    DataTable,
    VendorStatusBadge,
} from '~/components/ui';
import {
    Button,
    Checkbox,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    Switch,
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '~/components/ui/base';
import { type CloudProvider } from '~/types/cloud';

// 厂商账号类型定义
export interface VendorAccount {
    id: string;
    accountName: string;
    accountId: string;
    provider: CloudProvider;
    product: string;
    readOnlyMode: boolean;
    lastSyncTime: string;
    status: 'active' | 'inactive' | 'error';
    domains: number;
    // 同步状态相关
    syncStatus?: 'idle' | 'syncing' | 'synced' | 'error';
    lastSyncTimestamp?: string; // 详细的同步时间戳
}

interface VendorsTableProps {
    accounts: VendorAccount[];
    onToggleReadOnlyMode?: (accountId: string) => void;
    onEdit?: (account: VendorAccount) => void;
    onDelete?: (accountId: string) => void;
    onRefresh?: (accountId: string) => void;
    onSync?: (accountId: string) => void;
}

const getStatusText = (status: string) => {
    switch (status) {
        case 'active':
            return '正常';
        case 'inactive':
            return '未激活';
        case 'error':
            return '错误';
        default:
            return '未知';
    }
};

// 同步状态显示组件
function SyncStatusDisplay({
    account,
    onSync,
}: {
    account: VendorAccount;
    onSync?: (accountId: string) => void;
}) {
    const { syncStatus, lastSyncTime, lastSyncTimestamp } = account;

    if (syncStatus === 'syncing') {
        return (
            <div className='text-foreground flex items-center gap-2 text-xs'>
                {/* <Loader2 className='text-muted-foreground h-3 w-3 animate-[spin_1.5s_linear_infinite,pulse_3s_ease-in-out_infinite]' /> */}
                <Loader2 className='text-lilith-primary h-3 w-3 animate-[spin_1.5s_linear_infinite,pulse_3s_ease-in-out_infinite]' />
                <span>同步中</span>
            </div>
        );
    }

    if (syncStatus === 'synced') {
        return (
            <div className='text-foreground flex items-center gap-2 text-xs'>
                <CheckIcon className='h-3 w-3' />
                <Tooltip>
                    <TooltipTrigger asChild>
                        <span className='text-muted-foreground cursor-pointer underline decoration-dotted underline-offset-2'>
                            已同步
                        </span>
                    </TooltipTrigger>
                    <TooltipContent side='top' align='center'>
                        <p className='text-xs'>
                            <span className='text-muted'>同步时间: </span>
                            {lastSyncTimestamp || lastSyncTime}
                        </p>
                    </TooltipContent>
                </Tooltip>
            </div>
        );
    }

    if (syncStatus === 'error') {
        return <div className='text-lilith-primary text-xs'>同步失败</div>;
    }

    // 默认显示最近同步时间和同步按钮
    return (
        <div className='flex items-center gap-2'>
            <div className='text-muted-foreground text-xs'>{lastSyncTime}</div>
        </div>
    );
}

// 列定义
export function getColumns(
    onToggleReadOnlyMode?: (accountId: string) => void,
    onEdit?: (account: VendorAccount) => void,
    onDelete?: (accountId: string) => void,
    onRefresh?: (accountId: string) => void,
    onSync?: (accountId: string) => void,
): ColumnDef<VendorAccount>[] {
    return [
        {
            id: 'select',
            header: ({ table }) => (
                <div className='flex items-center justify-center'>
                    <Checkbox
                        checked={
                            table.getIsAllPageRowsSelected() ||
                            (table.getIsSomePageRowsSelected() &&
                                'indeterminate')
                        }
                        onCheckedChange={(value) =>
                            table.toggleAllPageRowsSelected(!!value)
                        }
                        aria-label='选择全部'
                    />
                </div>
            ),
            cell: ({ row }) => (
                <div className='flex items-center justify-center'>
                    <Checkbox
                        checked={row.getIsSelected()}
                        onCheckedChange={(value) => row.toggleSelected(!!value)}
                        aria-label='选择行'
                    />
                </div>
            ),
            enableSorting: false,
            enableHiding: false,
            size: 40, // 固定选择列宽度
        },
        {
            accessorKey: 'accountName',
            header: ({ column }) => {
                return (
                    <ActionButton
                        variant='ghost'
                        size='xs'
                        onClick={() =>
                            column.toggleSorting(column.getIsSorted() === 'asc')
                        }
                    >
                        账号信息
                        <ArrowUpDown />
                    </ActionButton>
                );
            },
            cell: ({ row }) => (
                <div>
                    <div className='text-xs'>{row.getValue('accountName')}</div>
                    <div className='text-muted-foreground text-xs'>
                        {row.original.accountId}
                    </div>
                </div>
            ),
        },
        {
            accessorKey: 'provider',
            header: '云服务商',
            cell: ({ row }) => {
                return (
                    <div className='flex items-center gap-2'>
                        <CloudIconWithText
                            provider={row.getValue('provider')}
                            size={16}
                        />
                    </div>
                );
            },
        },
        {
            accessorKey: 'product',
            header: '产品',
            cell: ({ row }) => (
                <div className='text-xs lowercase'>
                    {row.getValue('product')}
                </div>
            ),
        },
        {
            accessorKey: 'domains',
            header: ({ column }) => {
                return (
                    <ActionButton
                        variant='ghost'
                        onClick={() =>
                            column.toggleSorting(column.getIsSorted() === 'asc')
                        }
                        size='xs'
                    >
                        域名数量
                        <ArrowUpDown />
                    </ActionButton>
                );
            },
            cell: ({ row }) => (
                <div className='text-center text-xs font-normal'>
                    {row.getValue('domains')}
                </div>
            ),
        },
        {
            accessorKey: 'readOnlyMode',
            header: () => (
                <div className='flex items-center gap-1'>
                    只读模式
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <HelpCircle className='text-muted-foreground size-4 cursor-pointer' />
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>
                                开启后，该云账号只会进行从云服务商处同步数据，无法向云服务商执行写操作，如新建、删除或修改等
                            </p>
                        </TooltipContent>
                    </Tooltip>
                </div>
            ),
            cell: ({ row }) => {
                const account = row.original;
                return (
                    <Switch
                        checked={account.readOnlyMode}
                        onCheckedChange={() =>
                            onToggleReadOnlyMode?.(account.id)
                        }
                        // className='h-[0.875rem] w-6'
                    />
                );
            },
        },
        {
            accessorKey: 'status',
            header: '状态',
            cell: ({ row }) => {
                const status = row.getValue('status') as string;
                return (
                    <VendorStatusBadge
                        status={status}
                        getStatusText={getStatusText}
                    />
                );
            },
            filterFn: (row, id, value) => {
                return value.includes(row.getValue(id));
            },
        },
        {
            accessorKey: 'lastSyncTime',
            header: ({ column }) => {
                return (
                    <ActionButton
                        variant='ghost'
                        onClick={() =>
                            column.toggleSorting(column.getIsSorted() === 'asc')
                        }
                        size='xs'
                    >
                        最后同步
                        <ArrowUpDown />
                    </ActionButton>
                );
            },
            cell: ({ row }) => (
                <SyncStatusDisplay account={row.original} onSync={onSync} />
            ),
        },
        {
            id: 'actions',
            enableHiding: false,
            cell: ({ row }) => {
                const account = row.original;

                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant='ghost' className='h-8 w-8 p-0'>
                                <span className='sr-only'>打开菜单</span>
                                <MoreHorizontal className='h-4 w-4' />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                            <DropdownMenuItem
                                onClick={() =>
                                    navigator.clipboard.writeText(account.id)
                                }
                                className='text-xs'
                            >
                                复制账号ID
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                                onClick={() => onEdit?.(account)}
                                className='text-xs'
                            >
                                <Pencil className='mr-1 h-3 w-3' />
                                编辑
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                onClick={() => onRefresh?.(account.id)}
                                className='text-xs'
                            >
                                <RotateCcw className='mr-1 h-3 w-3' />
                                刷新
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                variant='destructive'
                                onClick={() => onDelete?.(account.id)}
                                className='text-xs'
                            >
                                <Trash2 className='mr-1 h-3 w-3' />
                                删除
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ];
}

export function VendorsTable({
    accounts,
    onToggleReadOnlyMode,
    onEdit,
    onDelete,
    onRefresh,
    onSync,
}: VendorsTableProps) {
    const columns = React.useMemo(
        () =>
            getColumns(
                onToggleReadOnlyMode,
                onEdit,
                onDelete,
                onRefresh,
                onSync,
            ),
        [onToggleReadOnlyMode, onEdit, onDelete, onRefresh, onSync],
    );

    return (
        <TooltipProvider>
            <DataTable
                columns={columns}
                data={accounts}
                enableSorting={true}
                enablePagination={true}
                enableRowSelection={true}
                emptyText='没有结果'
                pageSize={10}
            />
        </TooltipProvider>
    );
}
