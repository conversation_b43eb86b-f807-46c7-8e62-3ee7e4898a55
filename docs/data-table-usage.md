# DataTable 通用表格组件使用指南

## 概述

`DataTable` 是一个基于 TanStack Table 的通用表格组件，支持简单模式和高级模式，可以满足不同场景的表格需求。

## 特性

- **简单模式**: 基础表格展示，无排序、筛选等功能
- **高级模式**: 支持排序、筛选、分页、行选择等功能
- **灵活配置**: 可自定义样式、加载状态、空状态等
- **类型安全**: 完整的 TypeScript 支持

## 基本用法

### 1. 简单模式（推荐用于详情页面的配置表格）

```tsx
import { DataTable } from '~/components/ui';
import { type ColumnDef } from '@tanstack/react-table';

// 定义数据类型
interface CacheRule {
    id: string;
    priority: number;
    matchType: string;
    cacheTime: string;
}

// 定义列
const columns: ColumnDef<CacheRule>[] = [
    {
        accessorKey: 'priority',
        header: '优先级',
        cell: ({ row }) => (
            <Badge>{row.getValue('priority')}</Badge>
        ),
    },
    {
        accessorKey: 'matchType',
        header: '匹配类型',
    },
    {
        accessorKey: 'cacheTime',
        header: '缓存时间',
    },
];

// 使用组件
<DataTable
    columns={columns}
    data={cacheRules}
    simple={true}
    emptyText="暂无缓存规则"
/>
```

### 2. 高级模式（推荐用于列表页面）

```tsx
<DataTable
    columns={columns}
    data={domains}
    enableSorting={true}
    enablePagination={true}
    enableRowSelection={true}
    pageSize={20}
    isLoading={isLoading}
    onRowClick={(row) => console.log('点击行:', row)}
/>
```

## API 参考

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `columns` | `ColumnDef<TData, TValue>[]` | - | 表格列定义 |
| `data` | `TData[]` | - | 表格数据 |
| `simple` | `boolean` | `false` | 是否启用简单模式 |
| `enableSorting` | `boolean` | `true` | 是否启用排序 |
| `enableFiltering` | `boolean` | `true` | 是否启用筛选 |
| `enablePagination` | `boolean` | `false` | 是否启用分页 |
| `enableRowSelection` | `boolean` | `false` | 是否启用行选择 |
| `className` | `string` | - | 表格容器样式类 |
| `headerClassName` | `string` | - | 表头样式类 |
| `cellClassName` | `string` | - | 单元格样式类 |
| `isLoading` | `boolean` | `false` | 加载状态 |
| `loadingComponent` | `ReactNode` | - | 自定义加载组件 |
| `emptyComponent` | `ReactNode` | - | 自定义空状态组件 |
| `emptyText` | `string` | `'暂无数据'` | 空状态文本 |
| `pageSize` | `number` | `10` | 每页显示数量 |
| `onRowClick` | `(row: TData) => void` | - | 行点击事件 |

## 使用场景

### 1. 详情页面的配置表格

- 使用 `simple={true}`
- 不需要排序、分页等功能
- 专注于数据展示

### 2. 列表页面的数据表格

- 使用高级模式
- 启用排序、分页、筛选等功能
- 支持批量操作

### 3. 弹窗中的选择表格

- 启用行选择功能
- 可配置分页大小
- 支持搜索筛选

## 迁移指南

### 从现有表格迁移到 DataTable

1. **提取列定义**

   ```tsx
   // 原来的 JSX 表格
   <TableHead>优先级</TableHead>
   <TableHead>匹配规则</TableHead>
   
   // 转换为列定义
   const columns: ColumnDef<DataType>[] = [
       {
           accessorKey: 'priority',
           header: '优先级',
       },
       {
           accessorKey: 'matchRule',
           header: '匹配规则',
       },
   ];
   ```

2. **替换表格组件**

   ```tsx
   // 原来的实现
   <Table>
       <TableHeader>...</TableHeader>
       <TableBody>...</TableBody>
   </Table>
   
   // 新的实现
   <DataTable
       columns={columns}
       data={data}
       simple={true}
   />
   ```

3. **处理复杂单元格**

   ```tsx
   {
       accessorKey: 'actions',
       header: '操作',
       cell: ({ row }) => (
           <div className="flex gap-2">
               <Button onClick={() => edit(row.original)}>编辑</Button>
               <Button onClick={() => delete(row.original.id)}>删除</Button>
           </div>
       ),
   }
   ```

## 最佳实践

1. **列定义复用**: 将常用的列定义提取为独立函数
2. **类型安全**: 始终为数据定义 TypeScript 接口
3. **性能优化**: 对于大数据集，启用分页功能
4. **用户体验**: 提供合适的加载状态和空状态
5. **一致性**: 在同类型页面中保持表格样式一致

## 示例

查看以下文件了解具体实现：

- `app/components/domains/detail-cache-card.tsx` - 简单模式示例
- `app/components/vendors/vendors-table.tsx` - 高级模式示例（已重构）
- `app/components/domains/table-simple.tsx` - 高级模式示例（演示版本）

## 文件位置

DataTable 组件位于：`app/components/ui/extras/data-table.tsx`

通过以下方式导入：

```tsx
import { DataTable } from '~/components/ui';
```
