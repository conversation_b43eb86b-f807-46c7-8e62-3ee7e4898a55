# Checkbox 对齐问题修复

## 问题描述

在启用 `enableRowSelection` 时，表头的 checkbox 和行的 checkbox 没有正确对齐。

## 问题原因

1. **Padding 不一致**: 表头第一列使用 `pl-8`，而行第一列使用 `pl-4`
2. **缺少居中对齐**: checkbox 没有在单元格中居中显示
3. **列宽不固定**: 选择列的宽度没有固定，可能导致布局不稳定

## 修复方案

### 1. 修复 DataTable 组件的 padding 逻辑

**文件**: `app/components/ui/extras/data-table.tsx`

**修复前**:
```tsx
className={cn(
    'text-muted-foreground text-xs',
    index === 0 && 'pl-8',
    headerClassName,
)}
```

**修复后**:
```tsx
className={cn(
    'text-muted-foreground text-xs',
    index === 0 && (enableRowSelection ? 'pl-4' : 'pl-8'),
    headerClassName,
)}
```

**说明**: 当启用行选择时，表头第一列使用 `pl-4` 与行保持一致；否则使用 `pl-8`。

### 2. 优化 VendorsTable 中的 checkbox 对齐

**文件**: `app/components/vendors/vendors-table.tsx`

**修复前**:
```tsx
header: ({ table }) => (
    <Checkbox ... />
),
cell: ({ row }) => (
    <Checkbox ... />
),
```

**修复后**:
```tsx
header: ({ table }) => (
    <div className='flex items-center justify-center'>
        <Checkbox ... />
    </div>
),
cell: ({ row }) => (
    <div className='flex items-center justify-center'>
        <Checkbox ... />
    </div>
),
size: 40, // 固定选择列宽度
```

**说明**: 
- 使用 flexbox 将 checkbox 在单元格中居中
- 固定选择列宽度为 40px，确保布局稳定

## 修复效果

### 修复前
- ❌ 表头和行的 checkbox 不对齐
- ❌ checkbox 在单元格中位置不固定
- ❌ 选择列宽度不稳定

### 修复后
- ✅ 表头和行的 checkbox 完美对齐
- ✅ checkbox 在单元格中居中显示
- ✅ 选择列宽度固定，布局稳定

## 适用范围

这个修复适用于所有使用 DataTable 组件并启用 `enableRowSelection` 的表格：

1. **VendorsTable** - 厂商管理表格
2. **未来的表格** - 所有使用 DataTable 的新表格都会自动受益

## 最佳实践

### 1. 选择列定义模板

```tsx
{
    id: 'select',
    header: ({ table }) => (
        <div className='flex items-center justify-center'>
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && 'indeterminate')
                }
                onCheckedChange={(value) =>
                    table.toggleAllPageRowsSelected(!!value)
                }
                aria-label='选择全部'
            />
        </div>
    ),
    cell: ({ row }) => (
        <div className='flex items-center justify-center'>
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label='选择行'
            />
        </div>
    ),
    enableSorting: false,
    enableHiding: false,
    size: 40, // 固定宽度
}
```

### 2. DataTable 使用

```tsx
<DataTable
    columns={columns}
    data={data}
    enableRowSelection={true}  // 启用行选择
    // 其他配置...
/>
```

## 测试验证

可以通过以下方式验证修复效果：

1. 访问厂商管理页面 (`/vendors`)
2. 观察表格中的 checkbox 对齐情况
3. 确认表头和行的 checkbox 完美对齐
4. 确认选择列宽度固定

## 总结

通过这次修复：

1. **解决了对齐问题** - checkbox 现在完美对齐
2. **提升了用户体验** - 界面更加整洁美观
3. **增强了一致性** - 所有表格的选择列都使用相同的样式
4. **提供了最佳实践** - 为未来的表格开发提供了标准模板

这是一个小而重要的 UI 改进，体现了对细节的关注和用户体验的重视。
